import type { DownloadItem, DownloadConfig, DownloadResponse, IDownloadController, DownloadControllerStatus, DownloadResult, LiveStreamDetectionResult } from '../../types/download';
import { DownloadControllerStatus as Status } from '../../types/download';
import { sendToContentScript } from './messageUtils';
import { convertFilenameExtension } from './videoUtils';
import { checkIsLiveStream } from './m3u8Parser';

// 下载控制器类
export class DownloadController implements IDownloadController {
  public status: DownloadControllerStatus = Status.IDLE;
  public progress = {
    percentage: 0,
    downloadedSize: 0,
    totalSize: undefined as number | undefined,
    speed: 0
  };

  private config: DownloadConfig;
  private abortController?: AbortController;
  private reader?: ReadableStreamDefaultReader<Uint8Array>;
  private chunks: Uint8Array[] = [];
  private lastLogTime = 0;
  private lastReceivedLength = 0;
  private userCancelled = false; // 标记是否为用户主动取消

  public onProgress?: (progress: { percentage: number; downloadedSize: number; totalSize?: number; speed?: number }) => void;
  public onStatusChange?: (status: DownloadControllerStatus) => void;

  constructor(config: DownloadConfig) {
    this.config = config;
  }

  private updateStatus(newStatus: DownloadControllerStatus) {
    this.status = newStatus;
    if (this.onStatusChange) {
      this.onStatusChange(newStatus);
    }
  }

  private updateProgress(update: Partial<typeof this.progress>) {
    Object.assign(this.progress, update);
    if (this.onProgress) {
      this.onProgress(this.progress);
    }
  }

  async start(): Promise<DownloadResult> {
    try {
      this.updateStatus(Status.DOWNLOADING);
      this.abortController = new AbortController();

      console.log(`开始下载: ${this.config.filename}`);

      const response = await fetch(this.config.url, {
        method: 'GET',
        credentials: 'omit',
        cache: 'no-cache',
        signal: this.abortController.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 获取文件信息
      const contentLength = response.headers.get('content-length');
      const totalSize = contentLength ? parseInt(contentLength) : undefined;
      const contentType = response.headers.get('content-type') || 'application/octet-stream';

      this.progress.totalSize = totalSize;
      console.log(`文件信息: 大小=${totalSize ? (totalSize / 1024 / 1024).toFixed(1) + 'MB' : '未知'}, 类型=${contentType}`);

      // 获取响应流
      this.reader = response.body?.getReader();
      if (!this.reader) {
        throw new Error('无法获取响应流');
      }

      // 读取数据
      await this.readStream();

      // 下载完成，创建 blob URL（直接使用chunks避免额外内存占用）
      const blob = new Blob(this.chunks, { type: contentType });
      const blobUrl = URL.createObjectURL(blob);

      this.updateStatus(Status.COMPLETED);
      console.log(`下载完成: ${this.config.filename}, 大小: ${(this.progress.downloadedSize / 1024 / 1024).toFixed(1)}MB`);

      return {
        success: true,
        filename: this.config.filename,
        blobUrl
      };

    } catch (error) {
      // 如果是用户主动取消，返回成功结果并保存已下载的内容
      if (this.userCancelled && this.chunks.length > 0) {
        console.log('用户停止录制，保存已录制内容');
        this.updateStatus(Status.COMPLETED);

        // 创建已下载内容的Blob
        const blob = new Blob(this.chunks);
        const blobUrl = URL.createObjectURL(blob);

        return {
          success: true,
          filename: this.config.filename,
          blobUrl
        };
      }

      // 真正的错误情况
      this.updateStatus(Status.ERROR);
      console.error('下载失败:', error);
      return {
        success: false,
        filename: this.config.filename,
        error: (error as Error).message
      };
    }
  }

  private async readStream(): Promise<void> {
    if (!this.reader) return;

    this.lastLogTime = Date.now();
    this.lastReceivedLength = 0;

    while (true) {
      const { done, value } = await this.reader.read();

      if (done) break;

      if (value) {
        this.chunks.push(value);
        this.progress.downloadedSize += value.length;

        const currentTime = Date.now();
        const percentage = this.progress.totalSize ?
          (this.progress.downloadedSize / this.progress.totalSize) * 100 : 0;

        // 每500ms更新一次进度
        if (currentTime - this.lastLogTime >= 500) {
          const timeDiff = (currentTime - this.lastLogTime) / 1000;
          const sizeDiff = this.progress.downloadedSize - this.lastReceivedLength;
          const speed = sizeDiff / timeDiff;

          this.updateProgress({
            percentage,
            speed
          });

          console.log(`下载进度: ${percentage.toFixed(1)}% (${(this.progress.downloadedSize / 1024 / 1024).toFixed(1)}MB${this.progress.totalSize ? '/' + (this.progress.totalSize / 1024 / 1024).toFixed(1) + 'MB' : ''}) 速度: ${formatSpeed(speed)}`);

          this.lastLogTime = currentTime;
          this.lastReceivedLength = this.progress.downloadedSize;
        }
      }
    }

    // 最后一次进度更新
    this.updateProgress({
      percentage: 100,
      speed: 0
    });
  }

  cancel(): void {
    this.userCancelled = true; // 标记为用户主动取消
    if (this.abortController) {
      this.abortController.abort();
    }
    console.log('下载已取消');
  }
}

// 通过内容脚本设置请求头（为后续下载做准备）
export function downloadFileWithHeaders(data: DownloadItem, pageTaskId: string): Promise<DownloadResponse> {
  return new Promise((resolve, reject) => {
    const messageListener = (event: Event) => {
      const responseData = (event as CustomEvent).detail;
      console.log('🔍 downloadFileWithHeaders 收到响应:', responseData);

      window.removeEventListener('DOWNLOAD_FILE_RESPONSE', messageListener);

      // 从响应数据中提取实际的结果
      const actualData = responseData.data || responseData;
      console.log('🔍 提取的实际数据:', actualData);

      if (actualData.success) {
        console.log('✅ 请求头设置成功:', actualData);
        resolve(actualData);
      } else {
        console.error('❌ 请求头设置失败:', actualData.error || '下载文件失败');
        reject(new Error(actualData.error || '下载文件失败'));
      }
    };

    window.addEventListener('DOWNLOAD_FILE_RESPONSE', messageListener);

    // 向内容脚本发送请求，包含页面任务ID
    sendToContentScript({
      type: 'DOWNLOAD_FILE_WITH_HEADERS',
      data: {
        requestId: data.requestId,
        url: data.url,
        filename: data.filename,
        requestHeaders: data.requestHeaders,
        pageUrl: data.pageUrl,
        pageTaskId: pageTaskId // 添加页面任务ID
      }
    });
  });
}

// 网页端下载处理函数（使用fetch + URL.createObjectURL）
export async function downloadInWebpage(
  config: DownloadConfig,
  onProgress?: (progress: { percentage: number; downloadedSize: number; totalSize?: number; speed?: number }) => void
): Promise<{ success: boolean; filename: string }> {
  try {
    console.log(`开始网页端下载: ${config.filename}`);

    // 在网页环境中发起fetch请求（会自动使用设置的请求头）
    const response = await fetch(config.url, {
      method: 'GET',
      credentials: 'omit', // 改为omit避免CORS问题
      cache: 'no-cache'
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    // 获取文件大小和类型
    const contentLength = response.headers.get('content-length');
    const totalSize = contentLength ? parseInt(contentLength) : undefined;
    const contentType = response.headers.get('content-type') || 'application/octet-stream';

    console.log(`文件信息: 大小=${totalSize ? (totalSize / 1024 / 1024).toFixed(1) + 'MB' : '未知'}, 类型=${contentType}`);

    // 获取响应流
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法获取响应流');
    }

    let receivedLength = 0;
    const chunks: Uint8Array[] = [];
    let lastLogTime = Date.now();
    let lastReceivedLength = 0;

    // 读取流数据
    while (true) {
      const { done, value } = await reader.read();

      if (done) break;

      if (value) {
        chunks.push(value);
        receivedLength += value.length;

        const currentTime = Date.now();
        const percentage = totalSize ? (receivedLength / totalSize) * 100 : 0;

        // 每200ms打印一次日志和更新进度
        if (currentTime - lastLogTime >= 200) {
          const timeDiff = (currentTime - lastLogTime) / 1000; // 转换为秒
          const sizeDiff = receivedLength - lastReceivedLength;
          const speed = sizeDiff / timeDiff; // 字节/秒

          console.log(`下载进度: ${percentage.toFixed(1)}% (${(receivedLength / 1024 / 1024).toFixed(1)}MB${totalSize ? '/' + (totalSize / 1024 / 1024).toFixed(1) + 'MB' : ''}) 速度: ${formatSpeed(speed)}`);

          // 调用进度回调
          if (onProgress) {
            onProgress({
              percentage,
              downloadedSize: receivedLength,
              totalSize,
              speed
            });
          }

          lastLogTime = currentTime;
          lastReceivedLength = receivedLength;
        }
      }
    }

    // 确保最后一次进度更新
    if (onProgress) {
      onProgress({
        percentage: 100,
        downloadedSize: receivedLength,
        totalSize: receivedLength,
        speed: 0
      });
    }

    console.log(`下载完成，准备保存文件: ${config.filename}, 大小: ${(receivedLength / 1024 / 1024).toFixed(1)}MB`);

    // 创建Blob和Object URL（直接使用chunks避免额外内存占用）
    const blob = new Blob(chunks, { type: contentType });
    const objectUrl = URL.createObjectURL(blob);

    // 转换文件名后缀（将m3u8和hlv改为mp4）
    const convertedFilename = convertFilenameExtension(config.filename);
    console.log(`准备保存文件: ${config.filename}${config.filename !== convertedFilename ? ` -> ${convertedFilename}` : ''}`);

    // 使用<a>标签下载文件（网页环境标准方式）
    const link = document.createElement('a');
    link.href = objectUrl;
    link.download = convertedFilename;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 下载开始后立即释放Object URL以节省内存
    URL.revokeObjectURL(objectUrl);

    console.log(`✅ 文件保存成功: ${convertedFilename}`);

    return { success: true, filename: convertedFilename };

  } catch (error) {
    console.error('网页端下载失败:', error);
    throw error;
  }
}

// 实际的保存API调用（在网站端执行，避免扩展限制）
export function saveM3u8File(blob: Blob, filename: string): Promise<{ success: boolean; filename: string }> {
  return new Promise((resolve, reject) => {
    try {
      // 转换文件名后缀（将m3u8和hlv改为mp4）
      const convertedFilename = convertFilenameExtension(filename);
      console.log(`开始保存M3U8文件: ${filename}${filename !== convertedFilename ? ` -> ${convertedFilename}` : ''}, 大小: ${(blob.size / 1024 / 1024).toFixed(1)}MB`);

      // 使用简单的下载方式，确保文件名正确
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = convertedFilename; // 使用转换后的文件名
      a.style.display = 'none';

      document.body.appendChild(a);
      a.click(); // 触发浏览器下载
      document.body.removeChild(a);

      // 立即释放 Object URL 以节省内存
      URL.revokeObjectURL(url);

      console.log(`✅ M3U8文件保存成功: ${convertedFilename}`);
      resolve({ success: true, filename: convertedFilename });
    } catch (error) {
      console.error(`❌ M3U8文件保存失败: ${(error as Error).message}`);
      reject(error);
    }
  });
}

// 格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// 格式化下载速度
export function formatSpeed(bytesPerSecond: number): string {
  return formatFileSize(bytesPerSecond) + '/s';
}

// 保存已下载的文件
export function saveDownloadedFile(blobUrl: string, filename: string): Promise<{ success: boolean; filename: string }> {
  return new Promise((resolve, reject) => {
    try {
      // 转换文件名后缀（将m3u8和hlv改为mp4）
      const convertedFilename = convertFilenameExtension(filename);
      console.log(`开始保存文件: ${filename}${filename !== convertedFilename ? ` -> ${convertedFilename}` : ''}`);

      // 创建下载链接
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = convertedFilename;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log(`✅ 文件保存成功: ${convertedFilename}`);
      resolve({ success: true, filename: convertedFilename });
    } catch (error) {
      console.error(`❌ 文件保存失败: ${(error as Error).message}`);
      reject(error);
    }
  });
}

// 创建下载控制器
export function createDownloadController(config: DownloadConfig): DownloadController {
  return new DownloadController(config);
}

/**
 * 通用直播流检测函数
 * 支持多种格式：m3u8、flv、mp4等
 * @param item 下载项目
 * @param response HTTP响应对象（可选）
 * @param m3u8Content m3u8内容（仅对m3u8格式）
 * @returns 直播流检测结果
 */
export async function detectLiveStream(
  item: DownloadItem,
  response?: Response,
  m3u8Content?: string
): Promise<LiveStreamDetectionResult> {
  const url = item.url.toLowerCase();
  const ext = item.ext?.toLowerCase() || '';

  // 对于m3u8格式，使用专门的检测逻辑
  if (item.isM3u8 || url.includes('.m3u8') || ext === 'm3u8') {
    if (m3u8Content) {
      return checkIsLiveStream(m3u8Content);
    }

    // 如果没有m3u8内容，尝试获取
    try {
      const m3u8Response = await fetch(item.url, {
        method: 'HEAD',
        headers: item.requestHeaders
          ? Object.fromEntries(item.requestHeaders.map(h => [h.name, h.value]))
          : {}
      });

      // 对于m3u8，如果无法获取Content-Length，可能是直播流
      const contentLength = m3u8Response.headers.get('content-length');
      if (!contentLength || contentLength === '0') {
        return {
          isLiveStream: true,
          hasEndList: false,
          reason: 'M3U8无法获取Content-Length'
        };
      }
    } catch (error) {
      console.warn('检测m3u8直播流时出错:', error);
    }

    return {
      isLiveStream: false,
      hasEndList: true,
      reason: 'M3U8格式但无法确定是否为直播流'
    };
  }

  // 对于其他格式（flv、mp4等），基于HTTP响应头检测
  let contentLength: string | null = null;

  if (response) {
    contentLength = response.headers.get('content-length');
  } else {
    // 如果没有响应对象，发送HEAD请求获取头信息
    try {
      const headResponse = await fetch(item.url, {
        method: 'HEAD',
        headers: item.requestHeaders
          ? Object.fromEntries(item.requestHeaders.map(h => [h.name, h.value]))
          : {}
      });
      contentLength = headResponse.headers.get('content-length');
    } catch (error) {
      console.warn('获取文件头信息失败，默认按点播文件处理:', error);
      // HEAD请求失败时，默认按点播文件处理，而不是直播流
      // 这样可以避免网络问题导致的误判
      return {
        isLiveStream: false,
        hasEndList: true,
        totalSize: 0,
        reason: 'HEAD请求失败，默认按点播文件处理'
      };
    }
  }

  // 检查Content-Length
  if (!contentLength || contentLength === '0') {
    // 某些服务器可能不返回Content-Length，但文件仍然是点播的
    // 只有在明确知道是直播流的情况下才判定为直播流
    // 对于常见的视频格式（mp4、flv等），默认按点播处理
    const isCommonVideoFormat = /\.(mp4|flv|avi|mkv|mov|wmv|webm)$/i.test(item.url);
    if (isCommonVideoFormat) {
      return {
        isLiveStream: false,
        hasEndList: true,
        totalSize: 0,
        reason: '常见视频格式且无Content-Length，默认按点播文件处理'
      };
    }

    return {
      isLiveStream: true,
      hasEndList: false,
      totalSize: 0,
      reason: '缺少Content-Length且非常见视频格式'
    };
  }

  const totalSize = parseInt(contentLength, 10);

  // 如果Content-Length存在且大于0，认为是点播文件
  return {
    isLiveStream: false,
    hasEndList: true,
    totalSize,
    reason: `有Content-Length: ${totalSize}字节`
  };
}
