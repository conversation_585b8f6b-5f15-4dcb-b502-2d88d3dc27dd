'use client'

import { useDownloader } from '../../hooks/download/useDownloader';
import DownloadCard from './download/DownloadCard';
import { IconArrowRight } from '@tabler/icons-react';
import { ExtensionStatus } from '../../types/download';

function App() {
  const {
    downloadData,
    downloadProgress,
    startDownloadWithData,
    saveToLocal,
    savedFiles,
    updateDownloadDataFilename,
    stopLiveRecording,
    extensionStatus
  } = useDownloader();

  return (
    < >
      {/* 插件检测中或未安装 - 显示禁用状态的立即开始按钮 */}
      {(extensionStatus === ExtensionStatus.DETECTING || extensionStatus === ExtensionStatus.NOT_INSTALLED) && (
        <button
          disabled={true}
          className="flex flex-row justify-center items-center px-6 py-3.5 gap-2 border-none rounded-lg cursor-pointer bg-blue-700 text-white"
          style={{
            width: '136px',
            height: '52px'
          }}
        >
          <span className="font-inter font-medium text-base leading-6 text-white">
            立即开始
          </span>

          <IconArrowRight
            size={16}
            color="#FFFFFF"
          />
        </button>
      )}

      {/* 插件已安装 - 显示下载卡片 */}
      {extensionStatus === ExtensionStatus.INSTALLED && (
        <>
          <DownloadCard
            downloadData={downloadData}
            downloadProgress={downloadProgress}
            startDownloadWithData={startDownloadWithData}
            saveToLocal={saveToLocal}
            savedFiles={savedFiles}
            updateDownloadDataFilename={updateDownloadDataFilename}
            stopLiveRecording={stopLiveRecording}
          />
        </>
      )}
    </>
  );
}

export default App;